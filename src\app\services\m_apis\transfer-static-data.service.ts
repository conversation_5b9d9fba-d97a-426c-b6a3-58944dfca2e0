
import { Injectable } from '@angular/core';
import { Region, Department, Employee, Position } from '../../enums/m_enums/transfer.model';

@Injectable({
  providedIn: 'root'
})
export class TransferStaticDataService {

  getRegions(): Region[] {
    return [
      { id: 'R1', name: 'Chennai', code: 'CHN', description: 'Chennai Regional Office' },
      { id: 'R2', name: 'Coimbatore', code: 'CBE', description: 'Coimbatore Regional Office' },
      { id: 'R3', name: 'Madurai', code: 'MDU', description: 'Madurai Regional Office' },
      { id: 'R4', name: 'Salem', code: 'SLM', description: 'Salem Regional Office' },
      { id: 'R5', name: 'Trichy', code: 'TRY', description: 'Trichy Regional Office' }
    ];
  }

  getDepartments(): Department[] {
    return [
      { id: 'HR', name: 'Human Resources', code: 'HR', description: 'Human Resources Department' },
      { id: 'FIN', name: 'Finance', code: 'FIN', description: 'Finance Department' },
      { id: 'OPS', name: 'Operations', code: 'OPS', description: 'Operations Department' },
      { id: 'IT', name: 'Information Technology', code: 'IT', description: 'IT Department' },
      { id: 'ADMIN', name: 'Administration', code: 'ADM', description: 'Administration Department' },
      { id: 'PROC', name: 'Procurement', code: 'PROC', description: 'Procurement Department' },
      { id: 'QUAL', name: 'Quality Assurance', code: 'QA', description: 'Quality Assurance Department' }
    ];
  }

  getDepartmentsByRegion(regionId: string): Department[] {
    // In a real application, this would filter based on region
    return this.getDepartments();
  }

  getEmployees(): Employee[] {
    return [
      {
        id: 'E1001',
        name: 'Rajesh Kumar',
        position: 'Assistant Manager',
        region: 'R1',
        department: 'HR',
        employeeCode: 'EMP001',
        email: '<EMAIL>',
        mobileNumber: '9876543210',
        joiningDate: new Date('2020-01-15'),
        currentGrade: 'Grade 3',
        currentSalary: 45000
      },
      {
        id: 'E1002',
        name: 'Priya S',
        position: 'Junior Assistant',
        region: 'R2',
        department: 'FIN',
        employeeCode: 'EMP002',
        email: '<EMAIL>',
        mobileNumber: '9876543211',
        joiningDate: new Date('2021-03-10'),
        currentGrade: 'Grade 2',
        currentSalary: 35000
      },
      {
        id: 'E1003',
        name: 'Suresh Babu',
        position: 'Store Keeper',
        region: 'R3',
        department: 'OPS',
        employeeCode: 'EMP003',
        email: '<EMAIL>',
        mobileNumber: '9876543212',
        joiningDate: new Date('2019-07-20'),
        currentGrade: 'Grade 1',
        currentSalary: 28000
      },
      {
        id: 'E1004',
        name: 'Anitha Devi',
        position: 'Senior Manager',
        region: 'R1',
        department: 'IT',
        employeeCode: 'EMP004',
        email: '<EMAIL>',
        mobileNumber: '9876543213',
        joiningDate: new Date('2018-05-12'),
        currentGrade: 'Grade 4',
        currentSalary: 65000
      },
      {
        id: 'E1005',
        name: 'Karthik Raj',
        position: 'Assistant Manager',
        region: 'R2',
        department: 'PROC',
        employeeCode: 'EMP005',
        email: '<EMAIL>',
        mobileNumber: '9876543214',
        joiningDate: new Date('2020-09-01'),
        currentGrade: 'Grade 3',
        currentSalary: 48000
      }
    ];
  }

  getEmployeesByRegion(regionId: string): Employee[] {
    return this.getEmployees().filter(emp => emp.region === regionId);
  }

  getPositions(): Position[] {
    return [
      { id: 'P001', title: 'Junior Assistant', grade: 'Grade 1', department: 'HR', region: 'R1' },
      { id: 'P002', title: 'Assistant Manager', grade: 'Grade 3', department: 'HR', region: 'R1' },
      { id: 'P003', title: 'Senior Manager', grade: 'Grade 4', department: 'HR', region: 'R1' },
      { id: 'P004', title: 'Junior Assistant', grade: 'Grade 1', department: 'FIN', region: 'R1' },
      { id: 'P005', title: 'Assistant Manager', grade: 'Grade 3', department: 'FIN', region: 'R1' },
      { id: 'P006', title: 'Senior Manager', grade: 'Grade 4', department: 'FIN', region: 'R1' },
      { id: 'P007', title: 'Store Keeper', grade: 'Grade 1', department: 'OPS', region: 'R1' },
      { id: 'P008', title: 'Operations Executive', grade: 'Grade 2', department: 'OPS', region: 'R1' },
      { id: 'P009', title: 'Operations Manager', grade: 'Grade 3', department: 'OPS', region: 'R1' },
      { id: 'P010', title: 'System Administrator', grade: 'Grade 2', department: 'IT', region: 'R1' },
      { id: 'P011', title: 'IT Manager', grade: 'Grade 4', department: 'IT', region: 'R1' }
    ];
  }

  getPositionsByDepartment(departmentId: string): Position[] {
    return this.getPositions().filter(pos => pos.department === departmentId);
  }

  getCurrentEmployee(): Employee {
    // Return current logged-in employee (mock data)
    return this.getEmployees()[0]; // Rajesh Kumar
  }
}